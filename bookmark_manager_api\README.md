# 书签管理器后端API

这是一个基于Python Flask和MongoDB的书签管理器后端服务，为Vue2前端应用提供完整的API支持。

## 功能特性

- ✅ 书签的增删改查操作
- ✅ 标签管理和统计
- ✅ 多维度筛选和搜索
- ✅ 数据导入导出
- ✅ 批量操作支持
- ✅ 跨域请求支持(CORS)
- ✅ 完整的错误处理

## 技术栈

- **后端框架**: Flask 3.1.1
- **数据库**: MongoDB
- **Python版本**: 3.11+
- **主要依赖**: pymongo, flask-cors

## 项目结构

```
bookmark_manager_api/
├── src/
│   ├── models/
│   │   └── bookmark.py          # 书签数据模型
│   ├── routes/
│   │   └── bookmark.py          # 书签API路由
│   ├── database.py              # 数据库连接模块
│   └── main.py                  # Flask应用主文件
├── venv/                        # Python虚拟环境
├── requirements.txt             # 项目依赖
└── README.md                    # 项目说明
```

## 安装和运行

### 1. 环境准备

```bash
cd bookmark_manager_api
source venv/bin/activate
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动服务

```bash
python src/main.py
```

服务将在 `http://0.0.0.0:5000` 启动

## API 文档

### 基础信息

- **基础URL**: `http://localhost:5000/api`
- **数据格式**: JSON
- **字符编码**: UTF-8

### 健康检查

#### GET /api/health
检查服务状态

**响应示例**:
```json
{
  "success": true,
  "message": "书签管理器API服务正常运行",
  "version": "1.0.0"
}
```

### 书签管理

#### GET /api/bookmarks
获取书签列表

**查询参数**:
- `tags`: 标签筛选 (逗号分隔)
- `urgency`: 紧迫度筛选 (high/medium/low)
- `importance`: 重要度筛选 (1-5)
- `search`: 搜索关键词
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20)
- `sort`: 排序字段 (created_at/title/importance/urgency)
- `order`: 排序方向 (desc/asc)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "bookmarks": [...],
    "total": 100,
    "page": 1,
    "limit": 20,
    "pages": 5
  }
}
```

#### POST /api/bookmarks
创建新书签

**请求体**:
```json
{
  "title": "书签标题",
  "url": "https://example.com",
  "tags": ["标签1", "标签2"],
  "urgency": "high",
  "importance": 5,
  "reminder": "2025-07-10",
  "comment": "备注信息"
}
```

#### GET /api/bookmarks/{id}
获取单个书签详情

#### PUT /api/bookmarks/{id}
更新书签信息

**请求体**: 同创建书签，可部分更新

#### DELETE /api/bookmarks/{id}
删除单个书签

#### DELETE /api/bookmarks/batch
批量删除书签

**请求体**:
```json
{
  "ids": ["id1", "id2", "id3"]
}
```

#### POST /api/bookmarks/batch/tags
批量添加标签

**请求体**:
```json
{
  "bookmark_ids": ["id1", "id2"],
  "tags": ["新标签1", "新标签2"]
}
```

### 标签管理

#### GET /api/tags
获取标签统计

**响应示例**:
```json
{
  "success": true,
  "data": [
    {"name": "前端", "count": 4},
    {"name": "JavaScript", "count": 3}
  ]
}
```

### 统计信息

#### GET /api/stats
获取统计信息

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total_bookmarks": 100,
    "tags_count": 20,
    "urgency_stats": {
      "high": 10,
      "medium": 30,
      "low": 60
    },
    "importance_stats": {
      "1": 5,
      "2": 10,
      "3": 20,
      "4": 30,
      "5": 35
    }
  }
}
```

### 数据同步

#### GET /api/sync/export
导出所有书签数据

**响应示例**:
```json
{
  "success": true,
  "data": {
    "bookmarks": [...],
    "exported_at": "2025-07-03T20:14:31.000Z",
    "count": 100
  }
}
```

#### POST /api/sync/import
导入书签数据

**请求体**:
```json
{
  "bookmarks": [...]
}
```

## 数据库配置

### MongoDB连接信息
- **连接字符串**: `********************************************`
- **数据库名**: `bookmark_manager`
- **集合**: `bookmarks`, `tags`

### 数据模型

#### 书签文档结构
```json
{
  "_id": "ObjectId",
  "title": "string",
  "url": "string",
  "tags": ["string"],
  "urgency": "high|medium|low",
  "importance": 1-5,
  "reminder": "date",
  "comment": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## 错误处理

所有API都返回统一的错误格式：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述"
  }
}
```

常见错误码：
- `VALIDATION_ERROR`: 数据验证失败
- `BOOKMARK_NOT_FOUND`: 书签不存在
- `INVALID_DATA`: 请求数据无效
- `INTERNAL_ERROR`: 服务器内部错误

## 开发说明

### 添加新的API端点

1. 在 `src/routes/bookmark.py` 中添加新的路由函数
2. 在 `src/models/bookmark.py` 中添加相应的数据操作方法
3. 更新API文档

### 数据库索引

系统自动为以下字段创建索引以提高查询性能：
- `title`
- `tags`
- `urgency`
- `importance`
- `created_at`

### 部署注意事项

1. 确保MongoDB数据库可访问
2. 配置正确的CORS设置
3. 使用生产级WSGI服务器（如Gunicorn）
4. 设置环境变量管理敏感信息

## Railway 部署

### 快速部署

1. **准备代码仓库**
   - 确保代码已推送到 GitHub 仓库
   - 项目根目录包含所有必要的配置文件

2. **在 Railway 上创建项目**
   - 访问 [Railway.app](https://railway.app)
   - 点击 "New Project"
   - 选择 "Deploy from GitHub repo"
   - 选择你的仓库

3. **自动部署**
   - Railway 会自动检测到 Python 项目
   - 使用 `nixpacks.toml` 和 `Procfile` 进行构建和部署

### 环境变量配置

在 Railway 项目的 Variables 标签页中配置以下环境变量：

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| `SECRET_KEY` | Flask 应用密钥 | `your-super-secret-key-here` |
| `FLASK_ENV` | Flask 运行环境 | `production` |
| `MONGODB_URI` | MongoDB 连接字符串 | `********************************:port/` |
| `MONGODB_DATABASE` | 数据库名称 | `Honor` |
| `PORT` | 服务端口 (Railway 自动设置) | `自动分配` |

### 部署文件说明

项目包含以下 Railway 部署配置文件：

- **`Procfile`**: 定义应用启动命令
  ```
  web: gunicorn --bind 0.0.0.0:$PORT src.main:app
  ```

- **`railway.json`**: Railway 平台配置
  ```json
  {
    "build": {"builder": "NIXPACKS"},
    "deploy": {
      "startCommand": "gunicorn --bind 0.0.0.0:$PORT src.main:app",
      "healthcheckPath": "/api/health"
    }
  }
  ```

- **`nixpacks.toml`**: 构建配置
  ```toml
  [phases.setup]
  nixPkgs = ['python311']

  [start]
  cmd = 'gunicorn --bind 0.0.0.0:$PORT src.main:app'
  ```

- **`runtime.txt`**: Python 版本指定
  ```
  python-3.11.0
  ```

### 健康检查

Railway 使用 `/api/health` 端点进行应用健康检查。该端点返回：

```json
{
  "success": true,
  "message": "书签管理器API服务正常运行",
  "version": "1.0.0"
}
```

### 部署后验证

1. **检查部署状态**
   - 在 Railway 控制台查看部署日志
   - 确认应用状态为 "Active"

2. **测试 API 端点**
   ```bash
   curl https://your-app.railway.app/api/health
   ```

3. **验证数据库连接**
   ```bash
   curl https://your-app.railway.app/api/bookmarks
   ```

### 故障排除

**常见问题及解决方案：**

1. **构建失败**
   - 检查 `requirements.txt` 中的依赖版本
   - 确认 Python 版本兼容性

2. **应用启动失败**
   - 检查环境变量配置
   - 查看 Railway 部署日志

3. **数据库连接失败**
   - 验证 `MONGODB_URI` 格式正确
   - 确认数据库服务器可访问

4. **健康检查失败**
   - 确认 `/api/health` 端点正常响应
   - 检查应用启动时间是否超时

## 测试示例

### 创建书签
```bash
curl -X POST http://localhost:5000/api/bookmarks \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Vue.js 官方文档",
    "url": "https://vuejs.org/guide/introduction.html",
    "tags": ["前端", "文档", "Vue"],
    "urgency": "high",
    "importance": 5,
    "reminder": "2025-07-10",
    "comment": "需要学习 Vue 3 的新特性"
  }'
```

### 获取书签列表
```bash
curl -X GET "http://localhost:5000/api/bookmarks?tags=前端&page=1&limit=10"
```

### 获取统计信息
```bash
curl -X GET http://localhost:5000/api/stats
```

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。

