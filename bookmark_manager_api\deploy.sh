#!/bin/bash

# 书签管理器API部署脚本

echo "=== 书签管理器API部署脚本 ==="

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+')
if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
    echo "错误: 需要Python 3.8或更高版本，当前版本: $python_version"
    exit 1
fi

echo "✓ Python版本检查通过: $python_version"

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# Railway 部署检查
if [ "$1" = "railway" ]; then
    echo "=== Railway 部署准备 ==="

    # 检查必要的部署文件
    files=("Procfile" "railway.json" "nixpacks.toml" "runtime.txt" ".env.example")
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            echo "✗ 缺少 $file 文件"
            exit 1
        else
            echo "✓ $file 存在"
        fi
    done

    # 检查 requirements.txt 中是否包含 gunicorn
    if ! grep -q "gunicorn" requirements.txt; then
        echo "✗ requirements.txt 中缺少 gunicorn"
        exit 1
    else
        echo "✓ gunicorn 已包含在依赖中"
    fi

    echo ""
    echo "=== Railway 环境变量配置 ==="
    echo "请在 Railway 项目中配置以下环境变量："
    echo "  SECRET_KEY=your-super-secret-key"
    echo "  FLASK_ENV=production"
    echo "  MONGODB_URI=your-mongodb-connection-string"
    echo "  MONGODB_DATABASE=Honor"
    echo ""
    echo "=== 部署步骤 ==="
    echo "1. 将代码推送到 GitHub 仓库"
    echo "2. 在 Railway 中创建新项目"
    echo "3. 连接 GitHub 仓库"
    echo "4. 配置环境变量"
    echo "5. Railway 将自动部署应用"
    echo ""
    echo "Railway 部署准备完成！"
    exit 0
fi

# 安装依赖
echo "安装项目依赖..."
pip install -r requirements.txt

# 检查MongoDB连接
echo "检查MongoDB连接..."
python3 -c "
import pymongo
try:
    client = pymongo.MongoClient('*******************************************/', serverSelectionTimeoutMS=5000)
    client.admin.command('ping')
    print('✓ MongoDB连接成功')
except Exception as e:
    print(f'✗ MongoDB连接失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "MongoDB连接失败，请检查网络和数据库配置"
    exit 1
fi

echo ""
echo "=== 部署完成 ==="
echo ""
echo "启动服务:"
echo "  source venv/bin/activate"
echo "  python src/main.py"
echo ""
echo "服务地址: http://0.0.0.0:5000"
echo "API文档: http://0.0.0.0:5000/api/health"
echo ""
echo "测试API:"
echo "  curl http://localhost:5000/api/health"
echo ""

