#!/usr/bin/env python3
"""
Health check script for Railway deployment
This script can be used to verify the application is running correctly
"""

import requests
import sys
import os
from urllib.parse import urljoin

def check_health(base_url):
    """Check if the application health endpoint is responding"""
    try:
        health_url = urljoin(base_url, '/api/health')
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✓ Health check passed: {data.get('message', 'OK')}")
                return True
            else:
                print(f"✗ Health check failed: {data}")
                return False
        else:
            print(f"✗ Health check failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Health check failed: {e}")
        return False

def check_api_endpoints(base_url):
    """Check if main API endpoints are accessible"""
    endpoints = [
        '/api/bookmarks',
        '/api/tags',
        '/api/stats'
    ]
    
    for endpoint in endpoints:
        try:
            url = urljoin(base_url, endpoint)
            response = requests.get(url, timeout=10)
            
            if response.status_code in [200, 404]:  # 404 is OK for empty collections
                print(f"✓ {endpoint} is accessible")
            else:
                print(f"✗ {endpoint} returned status {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"✗ {endpoint} failed: {e}")
            return False
    
    return True

def main():
    # Get base URL from environment or command line
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = os.environ.get('APP_URL', 'http://localhost:5200')
    
    print(f"Checking application at: {base_url}")
    print("=" * 50)
    
    # Run health checks
    health_ok = check_health(base_url)
    api_ok = check_api_endpoints(base_url)
    
    print("=" * 50)
    
    if health_ok and api_ok:
        print("✓ All checks passed!")
        sys.exit(0)
    else:
        print("✗ Some checks failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
