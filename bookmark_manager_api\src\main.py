import os
import sys
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory, jsonify
from flask_cors import CORS
from src.database import init_database
from src.routes.bookmark import bookmark_bp

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))

# Use environment variable for secret key in production, fallback for development
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'asdf#FGSgvasgf$5$WGT')

# 配置CORS以允许跨域请求
CORS(app, origins="*", methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"], 
     allow_headers=["Content-Type", "Authorization"])

# 注册书签API蓝图
app.register_blueprint(bookmark_bp, url_prefix='/api')

# 初始化数据库连接
with app.app_context():
    success = init_database(app)
    if not success:
        print("警告: 数据库连接失败，应用可能无法正常工作")

# 健康检查端点
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'success': True,
        'message': '书签管理器API服务正常运行',
        'version': '1.0.0'
    })

# 静态文件服务（用于前端）
@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
        return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return jsonify({
                'success': True,
                'message': '书签管理器API服务',
                'endpoints': {
                    'health': '/api/health',
                    'bookmarks': '/api/bookmarks',
                    'tags': '/api/tags',
                    'stats': '/api/stats',
                    'sync': '/api/sync/export, /api/sync/import'
                }
            })

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': {
            'code': 'NOT_FOUND',
            'message': '请求的资源不存在'
        }
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': {
            'code': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }
    }), 500

if __name__ == '__main__':
    # Use PORT environment variable for Railway, fallback to 5200 for local development
    port = int(os.environ.get('PORT', 5200))
    debug_mode = os.environ.get('FLASK_ENV') != 'production'
    app.run(host='0.0.0.0', port=port, debug=debug_mode)

