# Railway 部署指南

本指南将帮助您将书签管理器API部署到Railway平台。

## 前置条件

1. GitHub 账户
2. Railway 账户 (可以使用 GitHub 登录)
3. 代码已推送到 GitHub 仓库

## 部署步骤

### 1. 准备代码仓库

确保您的仓库包含以下文件：

```
bookmark_manager_api/
├── src/                    # 应用源代码
├── requirements.txt        # Python 依赖
├── Procfile               # Railway 启动命令
├── railway.json           # Railway 配置
├── nixpacks.toml          # 构建配置
├── runtime.txt            # Python 版本
├── .env.example           # 环境变量示例
├── .gitignore             # Git 忽略文件
└── README.md              # 项目文档
```

### 2. 在 Railway 上创建项目

1. 访问 [Railway.app](https://railway.app)
2. 使用 GitHub 账户登录
3. 点击 "New Project"
4. 选择 "Deploy from GitHub repo"
5. 选择包含书签管理器API的仓库
6. 选择 `bookmark_manager_api` 目录（如果不在根目录）

### 3. 配置环境变量

在 Railway 项目的 "Variables" 标签页中添加以下环境变量：

| 变量名 | 值 | 说明 |
|--------|-----|------|
| `SECRET_KEY` | `your-super-secret-key-here` | Flask 应用密钥，请使用强密码 |
| `FLASK_ENV` | `production` | Flask 运行环境 |
| `MONGODB_URI` | `********************************:port/` | MongoDB 连接字符串 |
| `MONGODB_DATABASE` | `Honor` | 数据库名称 |

**注意**: `PORT` 变量由 Railway 自动设置，无需手动配置。

### 4. 部署验证

部署完成后，Railway 会提供一个公共URL。您可以通过以下方式验证部署：

1. **健康检查**
   ```bash
   curl https://your-app.railway.app/api/health
   ```

2. **API 测试**
   ```bash
   curl https://your-app.railway.app/api/bookmarks
   ```

3. **使用健康检查脚本**
   ```bash
   python healthcheck.py https://your-app.railway.app
   ```

## 配置文件说明

### Procfile
定义应用启动命令：
```
web: gunicorn --bind 0.0.0.0:$PORT src.main:app
```

### railway.json
Railway 平台配置：
```json
{
  "build": {"builder": "NIXPACKS"},
  "deploy": {
    "startCommand": "gunicorn --bind 0.0.0.0:$PORT src.main:app",
    "healthcheckPath": "/api/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### nixpacks.toml
构建环境配置：
```toml
[phases.setup]
nixPkgs = ['python311']

[phases.install]
cmds = [
    'pip install --upgrade pip',
    'pip install -r requirements.txt'
]

[start]
cmd = 'gunicorn --bind 0.0.0.0:$PORT src.main:app'
```

## 常见问题

### 1. 构建失败

**问题**: 依赖安装失败
**解决**: 检查 `requirements.txt` 中的包版本是否兼容

**问题**: Python 版本不匹配
**解决**: 确认 `runtime.txt` 中指定的 Python 版本

### 2. 应用启动失败

**问题**: 环境变量未配置
**解决**: 检查 Railway 项目中的环境变量设置

**问题**: 端口配置错误
**解决**: 确保应用使用 `$PORT` 环境变量

### 3. 数据库连接失败

**问题**: MongoDB 连接字符串错误
**解决**: 验证 `MONGODB_URI` 格式和凭据

**问题**: 网络访问限制
**解决**: 确保 MongoDB 服务器允许来自 Railway 的连接

### 4. 健康检查失败

**问题**: 健康检查超时
**解决**: 检查应用启动时间，可能需要增加超时时间

**问题**: 健康检查路径错误
**解决**: 确认 `/api/health` 端点正常工作

## 监控和日志

1. **查看部署日志**
   - 在 Railway 项目页面点击 "Deployments"
   - 选择最新的部署查看详细日志

2. **实时日志**
   - 在项目页面点击 "Logs" 查看应用运行日志

3. **性能监控**
   - Railway 提供基本的性能指标
   - 可以集成第三方监控服务

## 更新部署

1. **自动部署**
   - 推送代码到 GitHub 仓库
   - Railway 会自动触发新的部署

2. **手动部署**
   - 在 Railway 项目页面点击 "Deploy"
   - 选择要部署的分支或提交

## 域名配置

1. **自定义域名**
   - 在 Railway 项目设置中添加自定义域名
   - 配置 DNS 记录指向 Railway 提供的地址

2. **HTTPS**
   - Railway 自动为所有部署提供 HTTPS
   - 自定义域名也会自动获得 SSL 证书

## 成本优化

1. **资源使用**
   - Railway 按使用量计费
   - 监控应用资源使用情况

2. **休眠策略**
   - 配置应用在无流量时自动休眠
   - 减少不必要的资源消耗

## 支持

如果遇到问题：

1. 查看 Railway 官方文档
2. 检查项目日志和错误信息
3. 联系 Railway 支持团队
4. 参考本项目的 GitHub Issues

---

**祝您部署成功！** 🚀
